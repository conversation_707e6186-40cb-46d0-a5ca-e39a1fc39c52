import tensorflow as tf
from tensorflow.keras.preprocessing.image import load_img, img_to_array
import numpy as np
import os

# Chemin vers le modèle sauvegardé
model_path = "C:/Users/<USER>/OneDrive/Desktop/chat & chien/chat_chien_model.h5"

# Charger le modèle
model = tf.keras.models.load_model(model_path)

# Chemin vers l'image de test
test_image_path = "C:/Users/<USER>/OneDrive/Desktop/chat & chien/test_images/test_image.jpg"

# Préparer l'image pour la prédiction
img = load_img(test_image_path, target_size=(150, 150))
img_array = img_to_array(img)
img_array = np.expand_dims(img_array, axis=0)
img_array /= 255.0

# Faire la prédiction
prediction = model.predict(img_array)
print(prediction)

# Interpréter la prédiction
if prediction[0][0] < 0.5:
    print("Prédiction : Chat (0)")
else:
    print("Prédiction : Chien (1)")