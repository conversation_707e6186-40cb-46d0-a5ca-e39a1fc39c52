import os
from PIL import Image
import shutil

# Chemin vers le dossier principal du dataset
dataset_path = "C:/Users/<USER>/OneDrive/Desktop/chat & chien/PetImages"
corrupted_dir = os.path.join(dataset_path, "Corrupted")
os.makedirs(corrupted_dir, exist_ok=True)

def verify_and_clean_images(directory, label):
    corrupted_count = 0
    processed_count = 0
    for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)
        try:
            with Image.open(file_path) as img:
                img.verify()
                img = Image.open(file_path)
                # Vérifier la taille minimale pour éviter les images vides
                if img.size[0] == 0 or img.size[1] == 0:
                    raise ValueError("Image vide détectée")
                img = img.convert('RGB').resize((150, 150))
                new_filename = os.path.splitext(filename)[0] + '.jpg'
                img.save(os.path.join(directory, new_filename), 'JPEG')
                if new_filename != filename:
                    os.remove(file_path)
                processed_count += 1
        except (<PERSON><PERSON><PERSON><PERSON>, SyntaxError, ValueError) as e:
            print(f"Image corrompue ou invalide détectée : {file_path} ({str(e)}), déplacée vers Corrupted")
            shutil.move(file_path, os.path.join(corrupted_dir, f"{label}_{filename}"))
            corrupted_count += 1
    print(f"Dossier {label} : {processed_count} images traitées, {corrupted_count} images corrompues.")

# Nettoyer les dossiers Cat et Dog
verify_and_clean_images(os.path.join(dataset_path, "Cat"), "Cat")
verify_and_clean_images(os.path.join(dataset_path, "Dog"), "Dog")
print("Nettoyage terminé !")